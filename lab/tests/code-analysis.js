#!/usr/bin/env node

/**
 * EV.IO Code Analysis Script
 * Analyzes the minified evio.js file to extract anti-tampermonkey patterns
 */

const fs = require('fs');
const path = require('path');

class EvioAnalyzer {
    constructor(filePath) {
        this.filePath = filePath;
        this.code = '';
        this.patterns = {
            objectMethods: [],
            functionMethods: [],
            timingMethods: [],
            domMethods: [],
            eventMethods: [],
            debugMethods: [],
            cryptoMethods: [],
            suspiciousStrings: []
        };
    }
    
    loadCode() {
        try {
            this.code = fs.readFileSync(this.filePath, 'utf8');
            console.log(`Loaded ${this.code.length} characters from ${this.filePath}`);
            return true;
        } catch (error) {
            console.error('Failed to load code:', error.message);
            return false;
        }
    }
    
    // Extract patterns related to object manipulation
    analyzeObjectPatterns() {
        const objectPatterns = [
            /Object\.defineProperty\s*\([^)]+\)/g,
            /Object\.getOwnPropertyDescriptor\s*\([^)]+\)/g,
            /Object\.getOwnPropertyNames\s*\([^)]+\)/g,
            /Object\.keys\s*\([^)]+\)/g,
            /\.hasOwnProperty\s*\([^)]+\)/g,
            /\.propertyIsEnumerable\s*\([^)]+\)/g,
            /Object\.freeze\s*\([^)]+\)/g,
            /Object\.seal\s*\([^)]+\)/g
        ];
        
        objectPatterns.forEach((pattern, index) => {
            const matches = this.code.match(pattern) || [];
            if (matches.length > 0) {
                this.patterns.objectMethods.push({
                    pattern: pattern.source,
                    matches: matches.slice(0, 10), // Limit to first 10 matches
                    count: matches.length
                });
            }
        });
    }
    
    // Extract patterns related to function manipulation
    analyzeFunctionPatterns() {
        const functionPatterns = [
            /Function\.prototype\.toString/g,
            /\.toString\s*\(\s*\)/g,
            /\.constructor\s*\./g,
            /Function\.prototype\./g,
            /\.apply\s*\([^)]+\)/g,
            /\.call\s*\([^)]+\)/g,
            /\.bind\s*\([^)]+\)/g,
            /new\s+Function\s*\(/g
        ];
        
        functionPatterns.forEach((pattern, index) => {
            const matches = this.code.match(pattern) || [];
            if (matches.length > 0) {
                this.patterns.functionMethods.push({
                    pattern: pattern.source,
                    matches: matches.slice(0, 10),
                    count: matches.length
                });
            }
        });
    }
    
    // Extract timing-related patterns
    analyzeTimingPatterns() {
        const timingPatterns = [
            /performance\.now\s*\(\s*\)/g,
            /Date\.now\s*\(\s*\)/g,
            /requestAnimationFrame\s*\([^)]+\)/g,
            /setTimeout\s*\([^)]+\)/g,
            /setInterval\s*\([^)]+\)/g,
            /clearTimeout\s*\([^)]+\)/g,
            /clearInterval\s*\([^)]+\)/g
        ];
        
        timingPatterns.forEach((pattern, index) => {
            const matches = this.code.match(pattern) || [];
            if (matches.length > 0) {
                this.patterns.timingMethods.push({
                    pattern: pattern.source,
                    matches: matches.slice(0, 10),
                    count: matches.length
                });
            }
        });
    }
    
    // Extract DOM-related patterns
    analyzeDOMPatterns() {
        const domPatterns = [
            /document\.createElement\s*\([^)]+\)/g,
            /document\.getElementById\s*\([^)]+\)/g,
            /document\.querySelector\s*\([^)]+\)/g,
            /document\.querySelectorAll\s*\([^)]+\)/g,
            /\.appendChild\s*\([^)]+\)/g,
            /\.removeChild\s*\([^)]+\)/g,
            /\.insertBefore\s*\([^)]+\)/g,
            /MutationObserver\s*\(/g
        ];
        
        domPatterns.forEach((pattern, index) => {
            const matches = this.code.match(pattern) || [];
            if (matches.length > 0) {
                this.patterns.domMethods.push({
                    pattern: pattern.source,
                    matches: matches.slice(0, 10),
                    count: matches.length
                });
            }
        });
    }
    
    // Extract event-related patterns
    analyzeEventPatterns() {
        const eventPatterns = [
            /\.addEventListener\s*\([^)]+\)/g,
            /\.removeEventListener\s*\([^)]+\)/g,
            /\.dispatchEvent\s*\([^)]+\)/g,
            /new\s+Event\s*\(/g,
            /new\s+CustomEvent\s*\(/g,
            /\.preventDefault\s*\(\s*\)/g,
            /\.stopPropagation\s*\(\s*\)/g
        ];
        
        eventPatterns.forEach((pattern, index) => {
            const matches = this.code.match(pattern) || [];
            if (matches.length > 0) {
                this.patterns.eventMethods.push({
                    pattern: pattern.source,
                    matches: matches.slice(0, 10),
                    count: matches.length
                });
            }
        });
    }
    
    // Extract debugging and detection patterns
    analyzeDebugPatterns() {
        const debugPatterns = [
            /debugger\s*;/g,
            /console\./g,
            /eval\s*\(/g,
            /new\s+Function\s*\(/g,
            /\.stack\b/g,
            /Error\s*\(/g,
            /throw\s+/g
        ];
        
        debugPatterns.forEach((pattern, index) => {
            const matches = this.code.match(pattern) || [];
            if (matches.length > 0) {
                this.patterns.debugMethods.push({
                    pattern: pattern.source,
                    matches: matches.slice(0, 10),
                    count: matches.length
                });
            }
        });
    }
    
    // Extract crypto and random patterns
    analyzeCryptoPatterns() {
        const cryptoPatterns = [
            /Math\.random\s*\(\s*\)/g,
            /crypto\./g,
            /getRandomValues\s*\([^)]+\)/g,
            /btoa\s*\([^)]+\)/g,
            /atob\s*\([^)]+\)/g,
            /encodeURIComponent\s*\([^)]+\)/g,
            /decodeURIComponent\s*\([^)]+\)/g
        ];
        
        cryptoPatterns.forEach((pattern, index) => {
            const matches = this.code.match(pattern) || [];
            if (matches.length > 0) {
                this.patterns.cryptoMethods.push({
                    pattern: pattern.source,
                    matches: matches.slice(0, 10),
                    count: matches.length
                });
            }
        });
    }
    
    // Look for suspicious strings that might indicate detection
    analyzeSuspiciousStrings() {
        const suspiciousPatterns = [
            /"[^"]*(?:tamper|monkey|grease|script|inject|hook|debug|detect|cheat|hack)[^"]*"/gi,
            /'[^']*(?:tamper|monkey|grease|script|inject|hook|debug|detect|cheat|hack)[^']*'/gi,
            /`[^`]*(?:tamper|monkey|grease|script|inject|hook|debug|detect|cheat|hack)[^`]*`/gi
        ];
        
        suspiciousPatterns.forEach((pattern, index) => {
            const matches = this.code.match(pattern) || [];
            if (matches.length > 0) {
                this.patterns.suspiciousStrings.push({
                    pattern: pattern.source,
                    matches: matches.slice(0, 10),
                    count: matches.length
                });
            }
        });
    }
    
    // Extract function names and variable patterns
    extractIdentifiers() {
        // Extract single-letter variables (common in minified code)
        const singleLetterVars = this.code.match(/\b[a-zA-Z]\b/g) || [];
        const uniqueVars = [...new Set(singleLetterVars)];
        
        // Extract function declarations
        const functionDeclarations = this.code.match(/function\s+[a-zA-Z_$][a-zA-Z0-9_$]*\s*\(/g) || [];
        
        return {
            singleLetterVariables: uniqueVars,
            functionDeclarations: functionDeclarations.slice(0, 20)
        };
    }
    
    // Main analysis function
    analyze() {
        console.log('Starting comprehensive code analysis...');
        
        if (!this.loadCode()) {
            return false;
        }
        
        console.log('Analyzing object patterns...');
        this.analyzeObjectPatterns();
        
        console.log('Analyzing function patterns...');
        this.analyzeFunctionPatterns();
        
        console.log('Analyzing timing patterns...');
        this.analyzeTimingPatterns();
        
        console.log('Analyzing DOM patterns...');
        this.analyzeDOMPatterns();
        
        console.log('Analyzing event patterns...');
        this.analyzeEventPatterns();
        
        console.log('Analyzing debug patterns...');
        this.analyzeDebugPatterns();
        
        console.log('Analyzing crypto patterns...');
        this.analyzeCryptoPatterns();
        
        console.log('Analyzing suspicious strings...');
        this.analyzeSuspiciousStrings();
        
        console.log('Extracting identifiers...');
        const identifiers = this.extractIdentifiers();
        
        return {
            patterns: this.patterns,
            identifiers: identifiers,
            codeLength: this.code.length
        };
    }
    
    // Generate analysis report
    generateReport(results) {
        const report = {
            summary: {
                codeLength: results.codeLength,
                totalPatterns: Object.values(results.patterns).reduce((sum, category) => sum + category.length, 0),
                categories: Object.keys(results.patterns).map(key => ({
                    name: key,
                    patternCount: results.patterns[key].length,
                    totalMatches: results.patterns[key].reduce((sum, pattern) => sum + pattern.count, 0)
                }))
            },
            detailedFindings: results.patterns,
            identifiers: results.identifiers
        };
        
        return report;
    }
    
    // Save report to file
    saveReport(report, outputPath) {
        try {
            fs.writeFileSync(outputPath, JSON.stringify(report, null, 2));
            console.log(`Analysis report saved to ${outputPath}`);
            return true;
        } catch (error) {
            console.error('Failed to save report:', error.message);
            return false;
        }
    }
}

// Main execution
if (require.main === module) {
    const analyzer = new EvioAnalyzer('../../evio.js');
    const results = analyzer.analyze();
    
    if (results) {
        const report = analyzer.generateReport(results);
        analyzer.saveReport(report, './analysis-report.json');
        
        // Print summary
        console.log('\n=== ANALYSIS SUMMARY ===');
        console.log(`Code Length: ${report.summary.codeLength} characters`);
        console.log(`Total Pattern Categories: ${report.summary.categories.length}`);
        console.log(`Total Patterns Found: ${report.summary.totalPatterns}`);
        
        console.log('\nPattern Categories:');
        report.summary.categories.forEach(category => {
            console.log(`  ${category.name}: ${category.patternCount} patterns, ${category.totalMatches} matches`);
        });
        
        console.log('\nTop Suspicious Findings:');
        if (report.detailedFindings.suspiciousStrings.length > 0) {
            report.detailedFindings.suspiciousStrings.forEach(finding => {
                console.log(`  Pattern: ${finding.pattern}`);
                console.log(`  Matches: ${finding.count}`);
                if (finding.matches.length > 0) {
                    console.log(`  Examples: ${finding.matches.slice(0, 3).join(', ')}`);
                }
                console.log('');
            });
        } else {
            console.log('  No suspicious strings found (may be heavily obfuscated)');
        }
    }
}

module.exports = EvioAnalyzer;
