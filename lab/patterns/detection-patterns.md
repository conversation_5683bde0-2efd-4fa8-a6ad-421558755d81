# Anti-Tampermonkey Detection Patterns in EV.IO

## Overview
This document catalogs specific code patterns found in evio.js that are commonly used for anti-tampermonkey detection.

## 1. Timing-Based Detection

### Pattern: Performance Monitoring
```javascript
// Found patterns:
performance.now()
Date.now()
requestAnimationFrame()
```

**Purpose**: 
- Detect execution delays caused by script injection
- Monitor frame timing inconsistencies
- Identify debugging/breakpoint interference

**Detection Method**:
- Measure execution time of critical functions
- Compare expected vs actual timing
- Flag unusual delays as potential tampering

## 2. Object Integrity Checks

### Pattern: Property Descriptor Validation
```javascript
// Found patterns:
Object.defineProperty()
Object.getOwnPropertyDescriptor()
hasOwnProperty()
```

**Purpose**:
- Verify built-in objects haven't been modified
- Detect property hijacking
- Ensure function integrity

**Common Targets**:
- `Function.prototype`
- `Object.prototype`
- `Array.prototype`
- `XMLHttpRequest.prototype`

## 3. Function Constructor Monitoring

### Pattern: Constructor Validation
```javascript
// Found patterns:
Function()
constructor
toString()
```

**Purpose**:
- Detect if native functions have been replaced
- Verify function source code integrity
- Identify proxy objects or wrapped functions

**Detection Techniques**:
- Compare `toString()` output with expected values
- Check constructor properties
- Validate function length and name properties

## 4. DOM Manipulation Detection

### Pattern: Element Creation Monitoring
```javascript
// Found patterns:
document.createElement()
document.head
document.body
addEventListener()
removeEventListener()
```

**Purpose**:
- Detect unauthorized script injection
- Monitor for external stylesheets or scripts
- Track DOM modifications

**Monitoring Points**:
- Script tag creation
- Style tag injection
- Event listener modifications
- DOM tree changes

## 5. WebGL Fingerprinting

### Pattern: Graphics Context Analysis
```javascript
// Found patterns:
canvas
getContext()
WebGL
```

**Purpose**:
- Generate unique browser fingerprints
- Detect virtualized environments
- Identify automated browsers

**Techniques**:
- WebGL renderer information
- Canvas fingerprinting
- Graphics driver detection

## 6. Randomness and Entropy

### Pattern: Random Number Generation
```javascript
// Found patterns:
Math.random()
crypto
random
```

**Purpose**:
- Generate unpredictable values for detection
- Create unique session identifiers
- Implement anti-replay mechanisms

**Applications**:
- Challenge-response systems
- Timing variation
- Nonce generation

## 7. Event System Integrity

### Pattern: Event Handler Validation
```javascript
// Found patterns:
addEventListener()
removeEventListener()
dispatchEvent()
```

**Purpose**:
- Ensure event handlers haven't been hijacked
- Detect event interception
- Validate event propagation

**Checks**:
- Event listener count validation
- Handler function integrity
- Event timing analysis

## 8. Analytics and Tracking

### Pattern: ByteBrew Integration
```javascript
// Found patterns from ByteBrew SDK:
initializeByteBrew()
sendCustomEvent()
trackingEnabled
```

**Purpose**:
- Monitor user behavior patterns
- Detect automated interactions
- Track session anomalies

**Metrics**:
- Click patterns
- Mouse movement
- Keyboard timing
- Session duration

## 9. Code Obfuscation Indicators

### Pattern: Variable Name Mangling
```javascript
// Single-letter variables throughout:
var e, t, n, i, a, r, o, s, l, u, c, h, d, p, Q, f, m, g, v, y, w, b, x, _, k, j, z, q, T, S, M, E, A, P, L, N, C, R, O, I, F, D, B, U, H, V, W, G, X, Y, K, J, Z
```

**Purpose**:
- Make reverse engineering difficult
- Hide function purposes
- Complicate static analysis

## 10. String Obfuscation

### Pattern: Dynamic String Construction
```javascript
// Encoded strings and character manipulation
toString(16)
charCodeAt()
String.fromCharCode()
```

**Purpose**:
- Hide detection keywords
- Prevent static string analysis
- Obfuscate API calls

## Detection Evasion Strategies

### 1. Property Descriptor Spoofing
- Override `Object.getOwnPropertyDescriptor`
- Return expected values for monitored properties
- Maintain original behavior for non-monitored access

### 2. Timing Normalization
- Intercept timing functions
- Return consistent, expected values
- Avoid detection through timing analysis

### 3. Function Integrity Preservation
- Maintain original `toString()` output
- Preserve function properties
- Use transparent proxies

### 4. DOM Mutation Masking
- Hide injected elements from detection
- Use shadow DOM for isolation
- Intercept DOM query methods

### 5. Event System Bypass
- Use alternative event mechanisms
- Implement custom event handling
- Avoid standard DOM events

## Risk Assessment by Pattern

| Pattern | Detection Risk | Bypass Difficulty | Impact |
|---------|---------------|-------------------|---------|
| Timing Checks | High | Medium | High |
| Object Integrity | Very High | High | Critical |
| Function Validation | High | High | High |
| DOM Monitoring | Medium | Low | Medium |
| WebGL Fingerprinting | Medium | Medium | Low |
| Event System | High | Medium | High |

## Recommended Bypass Order

1. **Object Integrity** - Highest priority, most critical
2. **Function Validation** - Essential for script execution
3. **Timing Checks** - Prevents detection through performance
4. **Event System** - Required for user interaction simulation
5. **DOM Monitoring** - Lower priority, easier to bypass
6. **WebGL Fingerprinting** - Lowest priority, minimal impact
